import { Injectable } from '@nestjs/common';
import { OrderStatus } from '../domain/order.types';
import { OrderStatusHistoryRepository } from '../infrastructure/repositories/order-status-history.repository';
import { OrderRepository } from '../infrastructure/repositories/order.repository';
import { Order } from '../domain/order';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { HistoryService } from '../../../history/history.service';
import { HISTORY_ENTITIES } from '../../../history/infrastructure/constants/entity.constants';

@Injectable()
export class OrderStatusService {
  private readonly validTransitions: Record<OrderStatus, OrderStatus[]> = {
    [OrderStatus.Draft]: [
      OrderStatus.Submitted,
      OrderStatus.Pending,
      OrderStatus.Cancelled,
    ],
    [OrderStatus.Submitted]: [OrderStatus.Pending, OrderStatus.Cancelled],
    [OrderStatus.Pending]: [OrderStatus.Assigned, OrderStatus.Cancelled],
    [OrderStatus.Assigned]: [
      OrderStatus.InProgress,
      OrderStatus.Pending,
      OrderStatus.Cancelled,
    ],
    [OrderStatus.InProgress]: [OrderStatus.Completed, OrderStatus.Cancelled],
    [OrderStatus.Completed]: [],
    [OrderStatus.Cancelled]: [OrderStatus.Draft],
  };

  constructor(
    private readonly orderRepository: OrderRepository,
    private readonly historyService: HistoryService,
    private readonly statusHistoryRepository: OrderStatusHistoryRepository,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Check if a status transition is valid
   */
  isValidTransition(
    currentStatus: OrderStatus,
    newStatus: OrderStatus,
  ): boolean {
    return this.validTransitions[currentStatus].includes(newStatus);
  }

  /**
   * Get all valid next statuses for a given status
   */
  getValidNextStatuses(currentStatus: OrderStatus): OrderStatus[] {
    return this.validTransitions[currentStatus];
  }

  /**
   * Update the status of an order and record the status change in history
   * @returns Updated order or null if order not found or transition is invalid
   */
  async updateOrderStatus(
    order: Order,
    newStatus: OrderStatus,
    userId: string,
    reason?: string,
    comments?: string,
    locationData?: Record<string, any>,
  ): Promise<Order | null> {
    // Check if the transition is valid
    if (!this.isValidTransition(order.status, newStatus)) {
      return null;
    }

    // Update the order status
    const previousStatus = order.status;
    const updatedOrder = await this.orderRepository.updateStatus(
      order.id,
      newStatus,
      userId,
    );

    if (!updatedOrder) {
      return null;
    }

    // Record the status change in history
    await this.statusHistoryRepository.create(
      order.id,
      previousStatus,
      newStatus,
      userId,
      reason,
      comments,
      locationData,
    );

    await this.historyService.create({
      tenantId: order.tenantId,
      entity: HISTORY_ENTITIES.ORDER,
      entityId: order.id,
      property: 'status',
      oldValue: previousStatus,
      newValue: newStatus,
      createdBy: userId,
      updatedBy: userId,
    });

    // Emit status change event
    this.eventEmitter.emit('order.status.changed', {
      orderId: order.id,
      previousStatus,
      newStatus,
      userId,
    });

    return updatedOrder;
  }

  /**
   * Get the status history for an order
   */
  async getStatusHistory(orderId: string) {
    return this.statusHistoryRepository.findByOrderId(orderId);
  }

  /**
   * Get the latest status change for an order
   */
  async getLatestStatusChange(orderId: string) {
    return this.statusHistoryRepository.findLatestByOrderId(orderId);
  }
}
