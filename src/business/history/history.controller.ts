import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Req,
} from '@nestjs/common';
import { ApiCreatedResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { HistoryService } from './history.service';
import { AddressOperationNotAllowedException } from '../../utils/errors/exceptions/address-exceptions';

import { CreateHistoryDto } from './dto/create-history.dto';
import { RequestWithUser } from '../../core/auth/interceptors/tenant-context.interceptor';
import { HistoryDomain } from './domain/history.domain';

@ApiTags('Business - History')
@Controller({
  path: 'history',
  version: '1',
})
export class HistoryController {
  constructor(
    private readonly historyService: HistoryService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a history' })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ description: 'Created' })
  async create(
    @Req() request: RequestWithUser,
    @Body() createHistoryDto: CreateHistoryDto,
  ): Promise<HistoryDomain> {
    const tenantId = request.tenantContext?.tenantId;
    if (!tenantId) {
      throw new AddressOperationNotAllowedException(
        request.user?.id || 'unknown',
        'create',
        'Insufficient tenant access permissions',
      );
    }
    try {
      //   const addressDomain = this.mapper.map(
      //     createAddressDto,
      //     CreateAddressDto,
      //     AddressDomain,
      //   );
      //   createHistoryDto.tenantId = tenantId;
      //   createHistoryDto = request.user?.sub;
      //   createHistoryDto.updatedBy = request.user?.sub;
      const address = await this.historyService.create(createHistoryDto);
      return address;
    } catch (error) {
      throw error;
    }
  }

  @Get(':entity/:entityId')
  @ApiOperation({ summary: 'Find history by entity and entity Id' })
  @HttpCode(HttpStatus.OK)
  @ApiCreatedResponse({ description: 'Created' })
  async find(
    @Param('entity') entity: string,
    @Param('entityId') entityId: string,
  ): Promise<HistoryDomain[]> {
    const history = await this.historyService.find(entity, entityId);
    return history;
  }
}
