import { createMap, forMember, mapFrom, Mapper } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { HistoryEntity } from '../entities/history.entity';
import { HistoryDomain } from '../../domain/history.domain';

@Injectable()
export class HistoryProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      createMap(
        mapper,
        HistoryEntity,
        HistoryDomain,
        forMember(
          (dest) => dest.createdBy,
          mapFrom((src) => src.createdByUser?.contactName),
        ),
        forMember(
          (dest) => dest.updatedBy,
          mapFrom((src) => src.updatedByUser?.contactName),
        ),
      );
    };
  }
}
